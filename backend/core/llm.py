from pydantic import BaseModel
from langchain.chat_models import init_chat_model
import json
import os

class ResponseFormat(BaseModel):
    grade: str
    feedback: str

# 设置 DeepSeek API 配置
os.environ["OPENAI_API_KEY"] = "***********************************"
os.environ["OPENAI_BASE_URL"] = "https://api.deepseek.com"

model = init_chat_model(
    "openai:deepseek-chat",
    temperature=0
)

json_model = init_chat_model(
    "openai:deepseek-chat",
    temperature=0,  # 设置为0以获得确定性输出
    model_kwargs={
        "response_format": {"type": "json_object"}  # 强制JSON输出
    }
)

def get_structured_evaluation(content: str) -> ResponseFormat:
    """使用JSON模式获取结构化评估结果"""
    # 系统提示词必须包含 "json" 字样和格式示例
    system_prompt = """
请对用户提供的内容进行评分，并以 JSON 格式输出结果。

EXAMPLE JSON OUTPUT:
{
    "grade": "优秀",
    "feedback": "内容质量很高，表达清晰"
}
"""
    
    user_prompt = f"请评估以下内容：{content}"
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    response = json_model.invoke(messages)
    
    # 解析 JSON 响应
    try:
        json_data = json.loads(response.content)
        return ResponseFormat(**json_data)
    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        print(f"原始响应: {response.content}")
        return ResponseFormat(grade="解析失败", feedback="JSON格式错误")

# 测试代码
def test_json_output():
    print("=== 测试 JSON 输出功能 ===\n")
    
    test_contents = [
        "人工智能是当今最重要的技术发展之一，它正在改变我们的生活方式。",
        "这篇文章写得很糟糕，逻辑混乱，表达不清。",
        "区块链技术具有去中心化、透明性和不可篡改的特点，在金融领域有广泛应用。"
    ]
    
    for i, content in enumerate(test_contents, 1):
        print(f"测试 {i}:")
        print(f"内容: {content}")
        
        try:
            result = get_structured_evaluation(content)
            print("✅ 调用成功！")
            print(f"评分: {result.grade}")
            print(f"反馈: {result.feedback}")
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        print("-" * 50)

# 对比测试：普通模式 vs JSON模式
def compare_models():
    print("\n=== 对比测试：普通模式 vs JSON模式 ===\n")
    
    test_content = "Python是一种简单易学的编程语言。"
    
    # 普通模式
    print("普通模式输出:")
    try:
        normal_response = model.invoke(f"请评估这个内容并给出评分和反馈：{test_content}")
        print(f"响应: {normal_response.content}")
    except Exception as e:
        print(f"❌ 普通模式错误: {e}")
    
    print("\n" + "="*30 + "\n")
    
    # JSON模式
    print("JSON模式输出:")
    try:
        json_result = get_structured_evaluation(test_content)
        print(f"✅ 结构化结果:")
        print(f"评分: {json_result.grade}")
        print(f"反馈: {json_result.feedback}")
    except Exception as e:
        print(f"❌ JSON模式错误: {e}")

if __name__ == "__main__":
    # 运行测试
    test_json_output()
    compare_models()